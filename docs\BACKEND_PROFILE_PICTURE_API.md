# Backend Profile Picture API Documentation

This document outlines the backend API requirements for the profile picture upload system that integrates with S3 storage.

## Overview

The profile picture system stores only image names (not full URLs) in the database for efficiency and easier migration. The frontend constructs full S3 URLs using helper functions.

## Database Schema

### User Collection (MongoDB)

```javascript
{
  _id: ObjectId,
  email: String,
  name: String,
  mobile: String,
  profileImage: String, // Only the image filename, e.g., "profile-user123-1234567890.jpg"
  // ... other user fields
  createdAt: Date,
  updatedAt: Date
}
```

## API Endpoints

### 1. Get User Profile
```
GET /api/v1/user/{userId}
```

**Headers:**
- `Authorization: Bearer {jwt_token}`

**Response:**
```json
{
  "success": true,
  "user": {
    "_id": "user123",
    "email": "<EMAIL>",
    "name": "<PERSON>",
    "mobile": "+1234567890",
    "profileImage": "profile-user123-1234567890.jpg",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 2. Update User Profile
```
PUT /api/v1/user/{userId}
```

**Headers:**
- `Authorization: Bearer {jwt_token}`
- `Content-Type: application/json`

**Request Body:**
```json
{
  "name": "John Doe",
  "mobile": "+1234567890",
  "profileImage": "profile-user123-1234567890.jpg"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User profile updated successfully",
  "user": {
    "_id": "user123",
    "email": "<EMAIL>",
    "name": "John Doe",
    "mobile": "+1234567890",
    "profileImage": "profile-user123-1234567890.jpg",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 3. Create User (Registration)
```
POST /user/api/v1/user
```

**Headers:**
- `Authorization: Bearer {jwt_token}`
- `Content-Type: application/json`

**Request Body:**
```json
{
  "groupRole": "CUSTOMER"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User created successfully",
  "user": {
    "_id": "user123",
    "email": "<EMAIL>",
    "name": "John Doe",
    "groupRole": "CUSTOMER",
    "profileImage": "",
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

## Implementation Guidelines

### Backend Implementation (Node.js/Express)

```javascript
// User Model (Mongoose)
const userSchema = new mongoose.Schema({
  email: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  mobile: String,
  profileImage: String, // Store only filename
  groupRole: { type: String, enum: ['CUSTOMER', 'PROVIDER'], default: 'CUSTOMER' },
  // ... other fields
}, {
  timestamps: true
});

// User Controller
const updateUser = async (req, res) => {
  try {
    const { userId } = req.params;
    const updateData = req.body;

    // Validate profileImage if provided
    if (updateData.profileImage && !isValidImageName(updateData.profileImage)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid profile image format'
      });
    }

    const updatedUser = await User.findByIdAndUpdate(
      userId,
      updateData,
      { new: true, runValidators: true }
    );

    if (!updatedUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      message: 'User profile updated successfully',
      user: updatedUser
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

// Helper function to validate image name format
const isValidImageName = (imageName) => {
  const imageNameRegex = /^profile-[\w-]+-\d+\.(jpg|jpeg|png|webp)$/i;
  return imageNameRegex.test(imageName);
};
```

### Authentication

All endpoints require JWT authentication. Extract user information from the JWT token to ensure users can only access/modify their own data.

```javascript
// Middleware to extract user from JWT
const authenticateUser = (req, res, next) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  
  if (!token) {
    return res.status(401).json({ message: 'No token provided' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(401).json({ message: 'Invalid token' });
  }
};
```

### Error Handling

```javascript
// Standard error responses
const errorResponses = {
  USER_NOT_FOUND: {
    status: 404,
    message: 'User not found'
  },
  INVALID_IMAGE_FORMAT: {
    status: 400,
    message: 'Invalid profile image format'
  },
  UNAUTHORIZED: {
    status: 401,
    message: 'Unauthorized access'
  },
  VALIDATION_ERROR: {
    status: 400,
    message: 'Validation failed'
  }
};
```

## Frontend Integration

The frontend uses these helper functions to work with profile images:

```typescript
// Convert image name to full S3 URL
const getProfilePictureUrl = (imageName: string) => {
  if (!imageName) return getFallbackProfilePicture();
  return `${CDN_URL}/profile-images/${imageName}`;
};

// Upload workflow
const uploadProfilePicture = async (file: File, userId: string) => {
  // 1. Upload to S3 and get image name
  const { imageName } = await uploadProfilePictureToS3(file, userId);
  
  // 2. Update user profile with image name
  await apiClient.put(`/api/v1/user/${userId}`, {
    profileImage: imageName
  });
  
  return imageName;
};
```

## Security Considerations

1. **File Validation**: Validate file types and sizes on both frontend and backend
2. **Authentication**: Ensure users can only update their own profiles
3. **Rate Limiting**: Implement rate limiting for upload endpoints
4. **Image Name Validation**: Validate image name format to prevent path traversal
5. **S3 Permissions**: Configure S3 bucket with appropriate read/write permissions

## Testing

Test the following scenarios:
1. Upload new profile picture
2. Update existing profile picture
3. Delete profile picture (set to empty string)
4. Handle invalid image formats
5. Handle unauthorized access
6. Handle missing user scenarios
